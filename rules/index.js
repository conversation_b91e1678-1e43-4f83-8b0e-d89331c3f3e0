const { NudgeRule } = require('../src/nudgeEngine');

class SleepMealTimingRule extends NudgeRule {
  constructor(params = {}) {
    super("sleep_meal_gap", "Encourage early dinner before sleep.");
    this.minHours = params.minHours || 4;
  }

  applies({ data }) {
    const lastMealTime = data.mealLogs?.date || null;
    const sleepTime = data.sleepLogs?.startTime || null;
    if (!lastMealTime || !sleepTime) return false;
    const gap = (new Date(sleepTime) - new Date(lastMealTime)) / (1000 * 60 * 60);
    return true;
    return (gap > 0) && (gap < this.minHours);
  }

  generateNudge() {
    return {
      type: "nudge",
      content: {
        title: "Too Close to Bedtime?",
        message: `Try to finish your last meal at least ${this.minHours} hours before bedtime.`
      },
      status: "open",
      timestamp: new Date().toISOString()
    };
  }
}

class StepTargetShortfallRule extends NudgeRule {
  constructor(params = {}) {
    super("step_target_shortfall", "Nudge user if behind on weekly step goal.");
    this.thresholdPercent = params.thresholdPercent || 0.8;
  }

  applies({ data }) {
    const { stepsThisWeek = 0, stepGoalPerDay = 0, daysLogged = 0 } = data || {};
    const expected = stepGoalPerDay * daysLogged;
    return stepsThisWeek < expected * this.thresholdPercent;
  }

  generateNudge() {
    return {
      type: "nudge",
      content: {
        title: "Step Up Your Week!",
        message: "You're a little behind on your step goal. A short walk today helps!"
      },
      status: "open",
      timestamp: new Date().toISOString()
    };
  }
}

class InactivityNudgeRule extends NudgeRule {
  constructor(params = {}) {
    super("user_inactivity", "Encourage return after period of inactivity.");
    this.inactiveDays = params.inactiveDays || 7;
  }

  applies({ data }) {
    const lastActiveDate = data.activityLogs.timestamp || null;
    if (!lastActiveDate) return false;
    const daysSince = (Date.now() - new Date(lastActiveDate)) / (1000 * 60 * 60 * 24);
    return daysSince >= this.inactiveDays;
  }

  generateNudge() {
    return {
      type: "nudge",
      content: {
        title: "We Miss You!",
        message: "We haven’t heard from you in a while. Ready to restart with a small step today?"
      },
      status: "open",
      timestamp: new Date().toISOString()
    };
  }
}

module.exports = {
  SleepMealTimingRule,
  StepTargetShortfallRule,
  InactivityNudgeRule
};